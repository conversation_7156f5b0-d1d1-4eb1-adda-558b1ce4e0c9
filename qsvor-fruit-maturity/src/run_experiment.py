import numpy as np
import pandas as pd
from sklearn.model_selection import RepeatedStratifiedKFold
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import cohen_kappa_score

from src.qkernel import build_zz_feature_map, fidelity_kernel_matrix
from src.svor import QuantumSVOR
from src.baselines import get_baseline_models

# --- Configuration ---
DATA_PATH = 'data/processed/tomato_features_12d.csv'
N_FEATURES = 12
N_CLASSES = 4
N_SPLITS = 5
N_REPEATS = 2
Q_REPS = 2  # Reps in the ZZFeatureMap
C_PARAM = 1.0 # SVM C hyperparameter

# --- 1. Load Data ---
print("Loading data...")
data = pd.read_csv(DATA_PATH)
X = data.drop('label', axis=1).values
y = data['label'].values
print(f"Loaded data with shape X: {X.shape}, y: {y.shape}")

# --- 2. Initialize Models ---
print("Initializing models...")
q_svor = QuantumSVOR(C=C_PARAM, num_classes=N_CLASSES)
baselines = get_baseline_models()
all_models = {'Q-SVOR': q_svor, **baselines}
results = {name: {'MAE': [], 'QWK': []} for name in all_models.keys()}

# --- 3. Setup Quantum Feature Map ---
print(f"Building quantum feature map for {N_FEATURES} qubits and {Q_REPS} reps...")
feature_map = build_zz_feature_map(num_features=N_FEATURES, reps=Q_REPS)

# --- 4. Cross-Validation Loop ---
print(f"Starting {N_REPEATS}x{N_SPLITS}-fold cross-validation...")
rskf = RepeatedStratifiedKFold(n_splits=N_SPLITS, n_repeats=N_REPEATS, random_state=42)

for fold, (train_idx, test_idx) in enumerate(rskf.split(X, y)):
    print(f"\n--- Fold {fold+1}/{N_SPLITS*N_REPEATS} ---")
    X_train, X_test = X[train_idx], X[test_idx]
    y_train, y_test = y[train_idx], y[test_idx]

    # --- A. Quantum Kernel Computation ---
    print("Computing quantum kernel matrices...")
    K_train = fidelity_kernel_matrix(X_train, X_train, feature_map)
    K_test = fidelity_kernel_matrix(X_test, X_train, feature_map)
    print("Kernel computation finished.")

    # --- B. Train and Evaluate All Models ---
    for name, model in all_models.items():
        print(f"Training and evaluating {name}...")
        if name == 'Q-SVOR':
            model.fit(K_train, y_train)
            preds = model.predict(K_test)
        else:
            model.fit(X_train, y_train)
            preds = model.predict(X_test)

        # Calculate metrics
        mae = mean_absolute_error(y_test, preds)
        qwk = cohen_kappa_score(y_test, preds, weights='quadratic')
        
        results[name]['MAE'].append(mae)
        results[name]['QWK'].append(qwk)
        print(f"{name} -> MAE: {mae:.4f}, QWK: {qwk:.4f}")

# --- 5. Aggregate and Report Final Results ---
print("\n--- Final Results ---")
summary = pd.DataFrame(index=results.keys(), columns=['Mean MAE', 'Std MAE', 'Mean QWK', 'Std QWK'])

for name in results:
    summary.loc[name, 'Mean MAE'] = np.mean(results[name]['MAE'])
    summary.loc[name, 'Std MAE'] = np.std(results[name]['MAE'])
    summary.loc[name, 'Mean QWK'] = np.mean(results[name]['QWK'])
    summary.loc[name, 'Std QWK'] = np.std(results[name]['QWK'])

print(summary)
summary.to_csv('results/metrics/final_comparison.csv')
print("\nResults saved to results/metrics/final_comparison.csv")
