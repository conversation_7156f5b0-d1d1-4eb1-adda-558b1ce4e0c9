from sklearn.svm import SVC
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import Ridge
from sklearn.ensemble import RandomForestClassifier
import numpy as np

class SimpleOrdinalRidge:
    """Simple ordinal regression using Ridge regression with threshold approach."""
    def __init__(self, alpha=1.0):
        self.alpha = alpha
        self.ridge = Ridge(alpha=alpha)
        self.thresholds = None

    def fit(self, X, y):
        # Fit ridge regression to predict continuous values
        self.ridge.fit(X, y)

        # Set thresholds as midpoints between consecutive class values
        unique_classes = np.unique(y)
        self.thresholds = []
        for i in range(len(unique_classes) - 1):
            self.thresholds.append((unique_classes[i] + unique_classes[i+1]) / 2)

        return self

    def predict(self, X):
        # Get continuous predictions
        continuous_preds = self.ridge.predict(X)

        # Convert to ordinal predictions using thresholds
        ordinal_preds = np.ones(len(continuous_preds))
        for i, threshold in enumerate(self.thresholds):
            ordinal_preds[continuous_preds > threshold] = i + 2

        return ordinal_preds.astype(int)

def get_baseline_models():
    """Returns a dictionary of initialized baseline models."""
    models = {
        'RBF-SVM (Nominal)': make_pipeline(
            StandardScaler(),
            SVC(gamma='auto', decision_function_shape='ovr')
        ),
        'RandomForest (Nominal)': RandomForestClassifier(n_estimators=100, random_state=42),
        'OrdinalRidge (Classical)': make_pipeline(
            StandardScaler(),
            SimpleOrdinalRidge(alpha=1.0)
        )
    }
    return models

# Note: The CORAL/CORN deep learning baselines would require a separate
# training script due to their different data input (images) and training loop.
# They are not included here for simplicity of the feature-based pipeline.
