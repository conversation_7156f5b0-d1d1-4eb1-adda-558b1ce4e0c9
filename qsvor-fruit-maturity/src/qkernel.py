import numpy as np
from qiskit.circuit.library import ZZFeatureMap
from qiskit.quantum_info import Statevector

def build_zz_feature_map(num_features: int, reps: int = 2):
    """Builds an entangling ZZFeatureMap circuit."""
    return ZZFeatureMap(feature_dimension=num_features, reps=reps, entanglement='linear')

def fidelity_kernel_matrix(X_train: np.ndarray, X_test: np.ndarray, feature_map) -> np.ndarray:
    """
    Computes the exact fidelity quantum kernel matrix K(X_test, X_train).
    Uses statevector simulation (no shot noise).
    """
    if X_train.ndim == 1: X_train = np.expand_dims(X_train, axis=0)
    if X_test.ndim == 1: X_test = np.expand_dims(X_test, axis=0)

    # Precompute statevectors for the training set
    train_statevectors = []
    for x in X_train:
        # Create parameter dictionary
        param_dict = {param: val for param, val in zip(feature_map.parameters, x)}
        bound_circuit = feature_map.assign_parameters(param_dict)
        train_statevectors.append(Statevector.from_instruction(bound_circuit))

    kernel_matrix = np.zeros((len(X_test), len(X_train)))

    for i, x_test in enumerate(X_test):
        # Create parameter dictionary for test sample
        param_dict = {param: val for param, val in zip(feature_map.parameters, x_test)}
        bound_circuit = feature_map.assign_parameters(param_dict)
        test_statevector = Statevector.from_instruction(bound_circuit)

        for j, train_sv in enumerate(train_statevectors):
            # Calculate fidelity: |<ψ(x_test)|ψ(x_train)>|^2
            fidelity = np.abs(test_statevector.data.conj() @ train_sv.data)**2
            kernel_matrix[i, j] = fidelity

    return kernel_matrix
