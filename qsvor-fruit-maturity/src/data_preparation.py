import os
import cv2
import numpy as np
import pandas as pd
from skimage.feature import graycomatrix, graycoprops, local_binary_pattern
from sklearn.decomposition import PCA
from tqdm import tqdm

def apply_gray_world(image):
    """Applies Gray-World color constancy."""
    # Split channels and calculate average for each
    r, g, b = cv2.split(image.astype(np.float32))
    r_avg, g_avg, b_avg = np.mean(r), np.mean(g), np.mean(b)
    
    # Calculate the overall average
    avg = (r_avg + g_avg + b_avg) / 3
    
    # Scale each channel
    r_new = np.clip((avg / r_avg) * r, 0, 255)
    g_new = np.clip((avg / g_avg) * g, 0, 255)
    b_new = np.clip((avg / b_avg) * b, 0, 255)
    
    return cv2.merge([r_new, g_new, b_new]).astype(np.uint8)

def extract_features(image_path, crop_box=None):
    """Extracts a feature vector from a single image."""
    img = cv2.imread(image_path)
    if img is None:
        return None

    # 1. Crop the fruit (if crop_box is provided, e.g., [x, y, w, h])
    if crop_box:
        x, y, w, h = crop_box
        img = img[y:y+h, x:x+w]

    # 2. Apply color constancy
    img_const = apply_gray_world(img)
    
    # 3. Extract Color Features
    lab_img = cv2.cvtColor(img_const, cv2.COLOR_BGR2Lab)
    hsv_img = cv2.cvtColor(img_const, cv2.COLOR_BGR2HSV)
    lab_mean = np.mean(lab_img, axis=(0, 1)) # L*, a*, b* means (3 dims)
    hsv_mean = np.mean(hsv_img, axis=(0, 1)) # H, S, V means (3 dims)
    
    # 4. Extract Texture Features
    gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # LBP
    lbp = local_binary_pattern(gray_img, P=8, R=1, method='uniform')
    lbp_hist, _ = np.histogram(lbp.ravel(), bins=np.arange(0, 11), range=(0, 10))
    lbp_hist = lbp_hist.astype("float")
    lbp_hist /= (lbp_hist.sum() + 1e-6) # 10 dims
    
    # GLCM
    glcm = graycomatrix(gray_img, distances=[5], angles=[0], levels=256, symmetric=True, normed=True)
    glcm_props = [graycoprops(glcm, prop)[0, 0] for prop in ['contrast', 'dissimilarity', 'homogeneity', 'energy']] # 4 dims

    # 5. Combine features
    feature_vector = np.hstack([lab_mean, hsv_mean, lbp_hist, glcm_props])
    return feature_vector

def process_dataset(image_dir, labels_df, output_path, n_components=12):
    """Processes all images, extracts features, applies PCA, and saves."""
    all_features = []
    valid_labels = []
    
    for idx, row in tqdm(labels_df.iterrows(), total=len(labels_df)):
        image_path = os.path.join(image_dir, row['filename'])
        features = extract_features(image_path)
        if features is not None:
            all_features.append(features)
            valid_labels.append(row['label'])

    X = np.array(all_features)
    y = np.array(valid_labels)
    
    # Apply PCA to reduce to n_components
    pca = PCA(n_components=n_components)
    X_pca = pca.fit_transform(X)
    
    print(f"Original feature dim: {X.shape[1]}, PCA reduced dim: {X_pca.shape[1]}")
    
    # Save to a single file
    final_df = pd.DataFrame(X_pca, columns=[f'pc_{i+1}' for i in range(n_components)])
    final_df['label'] = y
    final_df.to_csv(output_path, index=False)
    print(f"Processed data saved to {output_path}")

# Example usage:
if __name__ == '__main__':
    # Assume you have a 'labels.csv' with 'filename' and 'label' columns
    # labels_df = pd.read_csv('data/raw/laboro-tomato/labels.csv')
    # process_dataset(
    #     image_dir='data/raw/laboro-tomato/images',
    #     labels_df=labels_df,
    #     output_path='data/processed/tomato_features_12d.csv',
    #     n_components=12
    # )
    pass
