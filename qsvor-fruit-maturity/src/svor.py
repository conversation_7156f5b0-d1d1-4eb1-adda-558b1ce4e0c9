import numpy as np
from sklearn.svm import SVC

class QuantumSVOR:
    """
    An Ordinal SVM using a precomputed quantum kernel, implemented via
    a cumulative-link (K-1 binary classifiers) approach.
    """
    def __init__(self, C=1.0, num_classes=None):
        self.C = C
        self.num_classes = num_classes
        self.svms = []

    def _make_cumulative_targets(self, y):
        """Creates K-1 binary targets for tasks P(y > k)."""
        if self.num_classes is None:
            self.num_classes = int(y.max())
        
        # Labels are 1-based, tasks are P(y>1), P(y>2), ..., P(y>K-1)
        return [(y > k).astype(int) for k in range(1, self.num_classes)]

    def fit(self, K_train, y):
        """
        Fits the K-1 binary SVMs on the precomputed training kernel matrix.
        
        Args:
            K_train (np.ndarray): The (n_train, n_train) quantum kernel matrix.
            y (np.ndarray): The ordinal labels (1, 2, ..., K).
        """
        self.svms = []
        binary_targets = self._make_cumulative_targets(y)
        
        for yk in binary_targets:
            # Ensure there are two classes for the SVM to train on
            if len(np.unique(yk)) > 1:
                clf = SVC(kernel='precomputed', C=self.C, probability=True)
                clf.fit(K_train, yk)
                self.svms.append(clf)
        
        if not self.svms:
            raise ValueError("Could not train any SVMs. Check if labels `y` have more than one class.")

    def predict(self, K_test):
        """
        Predicts ordinal labels based on the number of positive decisions.

        Args:
            K_test (np.ndarray): The (n_test, n_train) quantum kernel matrix.

        Returns:
            np.ndarray: Predicted ordinal labels (1, 2, ..., K).
        """
        if not self.svms:
            # If no SVMs were trained, return all predictions as class 1
            return np.ones(K_test.shape[0], dtype=int)

        # Get decision function scores for each of the K-1 classifiers
        decision_scores = [clf.decision_function(K_test) for clf in self.svms]

        if len(decision_scores) == 1:
            # Only one classifier
            decisions = decision_scores[0].reshape(-1, 1)
        else:
            # Multiple classifiers
            decisions = np.column_stack(decision_scores)

        # Prediction is 1 + sum of classifiers that vote positive (score > 0)
        # This inherently enforces the threshold monotonicity.
        prediction = 1 + (decisions > 0).sum(axis=1)
        return prediction
