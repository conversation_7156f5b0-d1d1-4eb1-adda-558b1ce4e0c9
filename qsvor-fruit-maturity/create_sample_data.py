#!/usr/bin/env python3
"""
Create sample data for testing the Q-SVOR implementation.
This generates synthetic feature data that mimics the structure of 
processed fruit maturity features.
"""

import numpy as np
import pandas as pd
import os

def create_sample_data():
    """Create synthetic fruit maturity data for testing."""
    np.random.seed(42)
    
    # Generate 200 samples with 12 features (matching PCA output)
    n_samples = 200
    n_features = 12
    n_classes = 4
    
    # Create features with some structure that correlates with maturity
    # Features 0-2: Color-related (LAB means)
    # Features 3-5: HSV means  
    # Features 6-9: LBP histogram features
    # Features 10-11: GLCM texture features
    
    X = np.random.randn(n_samples, n_features)
    
    # Create ordinal structure: later classes have higher values in some features
    y = np.random.randint(1, n_classes + 1, n_samples)
    
    # Add some correlation between features and labels to make it realistic
    for i in range(n_samples):
        # Color features should correlate with maturity
        X[i, 0] += (y[i] - 2.5) * 0.5  # L* channel
        X[i, 1] += (y[i] - 2.5) * 0.3  # a* channel  
        X[i, 2] += (y[i] - 2.5) * 0.4  # b* channel
        
        # HSV features
        X[i, 3] += (y[i] - 2.5) * 0.2  # Hue
        X[i, 4] += (y[i] - 2.5) * 0.3  # Saturation
        X[i, 5] += (y[i] - 2.5) * 0.4  # Value
        
        # Some texture features might also correlate
        X[i, 10] += (y[i] - 2.5) * 0.2  # GLCM contrast
        X[i, 11] += (y[i] - 2.5) * 0.1  # GLCM energy
    
    # Create DataFrame
    feature_names = [f'pc_{i+1}' for i in range(n_features)]
    df = pd.DataFrame(X, columns=feature_names)
    df['label'] = y
    
    # Ensure output directory exists
    os.makedirs('data/processed', exist_ok=True)
    
    # Save to CSV
    output_path = 'data/processed/tomato_features_12d.csv'
    df.to_csv(output_path, index=False)
    
    print(f"Created sample data with {n_samples} samples and {n_features} features")
    print(f"Label distribution: {np.bincount(y)}")
    print(f"Data saved to {output_path}")
    
    return df

if __name__ == '__main__':
    create_sample_data()
