{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Q-SVOR for Fruit Maturity Classification - Exploration Notebook\n", "\n", "This notebook is for exploring the data and testing individual components of the Q-SVOR system."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../')\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.datasets import make_classification\n", "from sklearn.model_selection import train_test_split\n", "\n", "from src.qkernel import build_zz_feature_map, fidelity_kernel_matrix\n", "from src.svor import QuantumSVOR\n", "from src.baselines import get_baseline_models"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Test Quantum Kernel Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create synthetic data for testing\n", "np.random.seed(42)\n", "X_synthetic = np.random.randn(20, 4)  # 20 samples, 4 features\n", "y_synthetic = np.random.randint(1, 5, 20)  # Ordinal labels 1-4\n", "\n", "print(f\"Synthetic data shape: {X_synthetic.shape}\")\n", "print(f\"Label distribution: {np.bincount(y_synthetic)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test quantum feature map\n", "feature_map = build_zz_feature_map(num_features=4, reps=2)\n", "print(f\"Feature map created with {feature_map.num_qubits} qubits\")\n", "print(f\"Circuit depth: {feature_map.depth()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test kernel matrix computation\n", "X_train, X_test, y_train, y_test = train_test_split(X_synthetic, y_synthetic, test_size=0.3, random_state=42)\n", "\n", "K_train = fidelity_kernel_matrix(X_train, X_train, feature_map)\n", "K_test = fidelity_kernel_matrix(X_test, X_train, feature_map)\n", "\n", "print(f\"Training kernel matrix shape: {K_train.shape}\")\n", "print(f\"Test kernel matrix shape: {K_test.shape}\")\n", "print(f\"Kernel matrix diagonal (should be close to 1): {np.diag(K_train)[:5]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Test Q-SVOR Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Q-SVOR model\n", "q_svor = QuantumSVOR(C=1.0, num_classes=4)\n", "q_svor.fit(K_train, y_train)\n", "predictions = q_svor.predict(K_test)\n", "\n", "print(f\"Predictions: {predictions}\")\n", "print(f\"True labels: {y_test}\")\n", "print(f\"Number of SVMs trained: {len(q_svor.svms)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Test Baseline Models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test baseline models\n", "baselines = get_baseline_models()\n", "\n", "for name, model in baselines.items():\n", "    try:\n", "        model.fit(X_train, y_train)\n", "        preds = model.predict(X_test)\n", "        print(f\"{name}: Predictions shape {preds.shape}\")\n", "    except Exception as e:\n", "        print(f\"{name}: Error - {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualize Kernel Matrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize the quantum kernel matrix\n", "plt.figure(figsize=(8, 6))\n", "plt.imshow(K_train, cmap='viridis', aspect='auto')\n", "plt.colorbar(label='Kernel Value')\n", "plt.title('Quantum Kernel Matrix (Training Set)')\n", "plt.xlabel('Sample Index')\n", "plt.ylabel('Sample Index')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Loading Template\n", "\n", "Use this section when you have actual fruit maturity data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Template for loading actual data\n", "# data_path = '../data/processed/tomato_features_12d.csv'\n", "# if os.path.exists(data_path):\n", "#     data = pd.read_csv(data_path)\n", "#     X = data.drop('label', axis=1).values\n", "#     y = data['label'].values\n", "#     print(f\"Loaded real data with shape X: {X.shape}, y: {y.shape}\")\n", "#     print(f\"Label distribution: {np.bincount(y)}\")\n", "# else:\n", "#     print(f\"Data file not found at {data_path}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}