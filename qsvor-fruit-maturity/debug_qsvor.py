#!/usr/bin/env python3
"""
Debug script for Q-SVOR implementation.
"""

import numpy as np
from sklearn.model_selection import train_test_split

from src.qkernel import build_zz_feature_map, fidelity_kernel_matrix
from src.svor import QuantumSVOR

def debug_qsvor():
    """Debug Q-SVOR implementation."""
    print("=== Debugging Q-SVOR ===")
    
    # Create synthetic data
    np.random.seed(42)
    X = np.random.randn(50, 4)
    y = np.random.randint(1, 5, 50)  # Labels 1-4
    
    print(f"Original data: X.shape = {X.shape}, y.shape = {y.shape}")
    print(f"Label distribution: {np.bincount(y)}")
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    print(f"Train data: X_train.shape = {X_train.shape}, y_train.shape = {y_train.shape}")
    print(f"Test data: X_test.shape = {X_test.shape}, y_test.shape = {y_test.shape}")
    print(f"Train label distribution: {np.bincount(y_train)}")
    print(f"Test label distribution: {np.bincount(y_test)}")
    
    # Build quantum kernel
    feature_map = build_zz_feature_map(num_features=4, reps=1)
    print(f"Feature map: {feature_map.num_qubits} qubits")
    
    K_train = fidelity_kernel_matrix(X_train, X_train, feature_map)
    K_test = fidelity_kernel_matrix(X_test, X_train, feature_map)
    
    print(f"K_train.shape = {K_train.shape}")
    print(f"K_test.shape = {K_test.shape}")
    
    # Test Q-SVOR
    q_svor = QuantumSVOR(C=1.0, num_classes=4)
    
    print("Fitting Q-SVOR...")
    try:
        q_svor.fit(K_train, y_train)
        print(f"✓ Q-SVOR fitted with {len(q_svor.svms)} binary classifiers")
        
        print("Making predictions...")
        predictions = q_svor.predict(K_test)
        print(f"✓ Predictions: {predictions}")
        print(f"✓ Predictions shape: {predictions.shape}")
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_qsvor()
