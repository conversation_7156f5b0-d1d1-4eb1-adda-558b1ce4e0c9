#!/usr/bin/env python3
"""
Test script to validate the Q-SVOR implementation components.
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, cohen_kappa_score

# Test imports
try:
    from src.qkernel import build_zz_feature_map, fidelity_kernel_matrix
    from src.svor import QuantumSVOR
    from src.baselines import get_baseline_models
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    exit(1)

def test_quantum_kernel():
    """Test quantum kernel implementation."""
    print("\n=== Testing Quantum Kernel ===")
    
    # Create small synthetic dataset
    np.random.seed(42)
    X = np.random.randn(10, 4)  # 10 samples, 4 features
    
    # Build feature map
    feature_map = build_zz_feature_map(num_features=4, reps=1)
    print(f"✓ Feature map created with {feature_map.num_qubits} qubits")
    
    # Test kernel matrix computation
    K = fidelity_kernel_matrix(X, X, feature_map)
    print(f"✓ Kernel matrix computed: shape {K.shape}")
    
    # Check properties
    assert K.shape == (10, 10), "Kernel matrix should be square"
    assert np.allclose(np.diag(K), 1.0, atol=1e-10), "Diagonal should be 1"
    assert np.allclose(K, K.T, atol=1e-10), "Kernel should be symmetric"
    print("✓ Kernel matrix properties verified")
    
    return True

def test_qsvor():
    """Test Q-SVOR implementation."""
    print("\n=== Testing Q-SVOR ===")
    
    # Create synthetic data
    np.random.seed(42)
    X = np.random.randn(50, 4)
    y = np.random.randint(1, 5, 50)  # Labels 1-4
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # Build quantum kernel
    feature_map = build_zz_feature_map(num_features=4, reps=1)
    K_train = fidelity_kernel_matrix(X_train, X_train, feature_map)
    K_test = fidelity_kernel_matrix(X_test, X_train, feature_map)
    
    # Test Q-SVOR
    q_svor = QuantumSVOR(C=1.0, num_classes=4)
    q_svor.fit(K_train, y_train)
    predictions = q_svor.predict(K_test)
    
    print(f"✓ Q-SVOR trained with {len(q_svor.svms)} binary classifiers")
    print(f"✓ Predictions shape: {predictions.shape}")
    
    # Check prediction range
    assert np.all(predictions >= 1) and np.all(predictions <= 4), "Predictions should be in range [1,4]"
    print("✓ Prediction range verified")
    
    return True

def test_baselines():
    """Test baseline models."""
    print("\n=== Testing Baseline Models ===")
    
    # Create synthetic data
    np.random.seed(42)
    X = np.random.randn(100, 4)
    y = np.random.randint(1, 5, 100)
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    baselines = get_baseline_models()
    
    for name, model in baselines.items():
        try:
            model.fit(X_train, y_train)
            preds = model.predict(X_test)
            mae = mean_absolute_error(y_test, preds)
            print(f"✓ {name}: MAE = {mae:.3f}")
        except Exception as e:
            print(f"✗ {name}: Error - {e}")
            return False
    
    return True

def test_with_sample_data():
    """Test with the generated sample data."""
    print("\n=== Testing with Sample Data ===")
    
    # Load sample data
    try:
        data = pd.read_csv('data/processed/tomato_features_12d.csv')
        X = data.drop('label', axis=1).values
        y = data['label'].values
        print(f"✓ Sample data loaded: {X.shape[0]} samples, {X.shape[1]} features")
        print(f"✓ Label distribution: {np.bincount(y)}")
    except FileNotFoundError:
        print("✗ Sample data not found. Run create_sample_data.py first.")
        return False
    
    # Small test with first 50 samples
    X_small = X[:50]
    y_small = y[:50]
    
    X_train, X_test, y_train, y_test = train_test_split(X_small, y_small, test_size=0.3, random_state=42)
    
    # Test Q-SVOR
    feature_map = build_zz_feature_map(num_features=12, reps=1)  # Reduced reps for speed
    K_train = fidelity_kernel_matrix(X_train, X_train, feature_map)
    K_test = fidelity_kernel_matrix(X_test, X_train, feature_map)
    
    q_svor = QuantumSVOR(C=1.0, num_classes=4)
    q_svor.fit(K_train, y_train)
    q_preds = q_svor.predict(K_test)
    
    q_mae = mean_absolute_error(y_test, q_preds)
    q_qwk = cohen_kappa_score(y_test, q_preds, weights='quadratic')
    
    print(f"✓ Q-SVOR on sample data: MAE = {q_mae:.3f}, QWK = {q_qwk:.3f}")
    
    # Test one baseline
    baselines = get_baseline_models()
    model = baselines['RBF-SVM (Nominal)']
    model.fit(X_train, y_train)
    b_preds = model.predict(X_test)
    
    b_mae = mean_absolute_error(y_test, b_preds)
    b_qwk = cohen_kappa_score(y_test, b_preds, weights='quadratic')
    
    print(f"✓ RBF-SVM baseline: MAE = {b_mae:.3f}, QWK = {b_qwk:.3f}")
    
    return True

def main():
    """Run all tests."""
    print("Q-SVOR Implementation Test Suite")
    print("=" * 40)
    
    tests = [
        test_quantum_kernel,
        test_qsvor,
        test_baselines,
        test_with_sample_data
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} failed with exception: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{len(tests)} tests")
    
    if passed == len(tests):
        print("🎉 All tests passed! Implementation is ready.")
        return True
    else:
        print("❌ Some tests failed. Check the implementation.")
        return False

if __name__ == '__main__':
    main()
